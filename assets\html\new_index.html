<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ring Programming IDE - AI Agent</title>
    
    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/material-darker.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
     <!-- Theme style  -->
    <link rel="stylesheet" href="../css/style.css">
    
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1><i class="fas fa-ring"></i> Ring IDE</h1>
                <p>AI-Powered Development Environment</p>
            </div>

            <!-- Project Section -->
            <div class="section">
                <h3><i class="fas fa-folder-open"></i> إدارة المشروع</h3>
                <div class="input-group">
                    <input type="text" id="projectName" placeholder="اسم المشروع الجديد">
                    <button class="btn btn-primary" onclick="createProject()">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <button class="btn btn-success" onclick="openProject()">
                    <i class="fas fa-folder-open"></i> فتح مشروع
                </button>
                <button class="btn btn-warning" onclick="saveProject()">
                    <i class="fas fa-save"></i> حفظ المشروع
                </button>
            </div>

            <!-- File Management -->
            <div class="section">
                <h3><i class="fas fa-file-code"></i> إدارة الملفات</h3>
                <div class="input-group">
                    <input type="text" id="fileName" placeholder="اسم الملف الجديد">
                    <button class="btn btn-primary" onclick="createNewFile()">
                        <i class="fas fa-file-plus"></i>
                    </button>
                </div>
                <button class="btn btn-success" onclick="openFile()">
                    <i class="fas fa-file-import"></i> فتح ملف
                </button>
                <button class="btn btn-warning" onclick="saveFile()">
                    <i class="fas fa-save"></i> حفظ الملف
                </button>
                <button class="btn btn-danger" onclick="deleteFile()">
                    <i class="fas fa-trash"></i> حذف الملف
                </button>
            </div>

            <!-- File List -->
            <div class="section">
                <h3><i class="fas fa-list"></i> قائمة الملفات</h3>
                <div class="file-list" id="fileList">
                    <div class="file-item active">
                        <i class="fas fa-file-code"></i>
                        <span>main.ring</span>
                    </div>
                    <div class="file-item">
                        <i class="fas fa-file-code"></i>
                        <span>utils.ring</span>
                    </div>
                    <div class="file-item">
                        <i class="fas fa-file-alt"></i>
                        <span>README.md</span>
                    </div>
                </div>
            </div>

            <!-- Code Actions -->
            <div class="section">
                <h3><i class="fas fa-play"></i> تشغيل الكود</h3>
                <button class="btn btn-success" onclick="runCode()">
                    <i class="fas fa-play"></i> تشغيل
                </button>
                <button class="btn btn-warning" onclick="debugCode()">
                    <i class="fas fa-bug"></i> تصحيح الأخطاء
                </button>
                <button class="btn btn-primary" onclick="formatCode()">
                    <i class="fas fa-magic"></i> تنسيق الكود
                </button>
                <button class="btn btn-info" onclick="testConnection()">
                    <i class="fas fa-link"></i> اختبار الربط
                </button>
            </div>

            <!-- Connection Test Results -->
            <div class="section" id="testResults" style="display: none;">
                <h3><i class="fas fa-check-circle"></i> نتائج اختبار الربط</h3>
                <div id="connectionStatus" class="status-display" style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; margin: 10px 0; min-height: 100px; text-align: right; font-family: monospace;">
                    جاري الاختبار...
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h2><i class="fas fa-code"></i> محرر الكود</h2>
                <div class="header-actions">
                    <button class="header-btn" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i> الوضع الليلي
                    </button>
                    <button class="header-btn" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> ملء الشاشة
                    </button>
                    <button class="header-btn" onclick="showSettings()">
                        <i class="fas fa-cog"></i> الإعدادات
                    </button>
                </div>
            </div>

            <!-- Toolbar -->
            <div class="toolbar">
                <button class="btn btn-primary" onclick="newFile()">
                    <i class="fas fa-file-plus"></i> جديد
                </button>
                <button class="btn btn-success" onclick="openFile()">
                    <i class="fas fa-folder-open"></i> فتح
                </button>
                <button class="btn btn-warning" onclick="saveFile()">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button class="btn btn-danger" onclick="undoAction()">
                    <i class="fas fa-undo"></i> تراجع
                </button>
                <button class="btn btn-primary" onclick="redoAction()">
                    <i class="fas fa-redo"></i> إعادة
                </button>
                <button class="btn btn-success" onclick="findReplace()">
                    <i class="fas fa-search"></i> بحث واستبدال
                </button>
            </div>

            <!-- Editor Container -->
            <div class="editor-container">
                <div class="editor-wrapper">
                    <textarea id="codeEditor" placeholder="ابدأ بكتابة كود Ring هنا...">
# مرحباً بك في Ring Programming IDE
# هذا مثال بسيط لبرنامج Ring

load "stdlib.ring"

func main
    see "مرحباً بالعالم من Ring!" + nl
    see "هذا محرر كود متطور مع ذكاء اصطناعي" + nl

    # إنشاء قائمة
    aNumbers = [1, 2, 3, 4, 5]
    see "الأرقام: " + list2str(aNumbers) + nl

    # حلقة تكرار
    for i = 1 to 10
        see "العدد: " + i + nl
    next

    # استدعاء دالة
    result = calculateSum(10, 20)
    see "النتيجة: " + result + nl

func calculateSum(a, b)
    return a + b
                    </textarea>
                </div>

                <!-- Chat Panel -->
                <div class="chat-panel" id="chatPanel">
                    <div class="chat-header">
                        <h3><i class="fas fa-robot"></i> مساعد الذكاء الاصطناعي</h3>
                        <button class="chat-toggle" onclick="toggleChat()">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <div class="chat-messages" id="chatMessages">
                        <div class="message ai">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    مرحباً! أنا مساعدك الذكي في البرمجة. يمكنني مساعدتك في:
                                    <br>• كتابة وتحسين الكود
                                    <br>• إصلاح الأخطاء
                                    <br>• شرح المفاهيم البرمجية
                                    <br>• تحليل الكود وتحسين الأداء
                                </div>
                                <div class="message-time">الآن</div>
                            </div>
                        </div>

                        <div class="message system">
                            <div class="message-avatar">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    تم تحميل المشروع بنجاح. جاهز للبرمجة!
                                </div>
                                <div class="message-time">منذ دقيقة</div>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <div class="chat-input-group">
                            <textarea class="chat-input" id="chatInput" placeholder="اكتب سؤالك أو طلبك هنا..." rows="2"></textarea>
                            <button class="chat-send-btn" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-text">
                    <div class="status-indicator"></div>
                    <span id="statusText">جاهز للبرمجة</span>
                </div>
                <div class="status-info">
                    <span>السطر: 1 | العمود: 1 | Ring v1.19</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading" id="loadingOverlay">
        <div class="spinner"></div>
        <p>جاري المعالجة...</p>
    </div>

    <!-- CodeMirror JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/selection/active-line.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/search/search.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/search/searchcursor.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/dialog/dialog.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/dialog/dialog.css"></script>

    <!-- Application JavaScript -->
    <script src="../js/new_app.js"></script>




</body>
</html>
