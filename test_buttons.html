<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار الأزرار</title>
    <style>
        body { font-family: Arial; padding: 20px; }
        button { padding: 10px 20px; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>اختبار الأزرار</h1>
    
    <button onclick="testCreateProject()">إنشاء مشروع</button>
    <button onclick="testCreateFile()">إنشاء ملف</button>
    <button onclick="testSaveFile()">حفظ ملف</button>
    <button onclick="testRunCode()">تشغيل كود</button>
    <button onclick="testFormatCode()">تنسيق كود</button>
    <button onclick="testAnalyzeCode()">تحليل كود</button>
    <button onclick="testSendChat()">إرسال رسالة</button>
    
    <div id="result" class="result">النتائج ستظهر هنا...</div>
    
    <script>
        function testCreateProject() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر إنشاء مشروع';
            console.log('Create project button clicked');
        }
        
        function testCreateFile() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر إنشاء ملف';
            console.log('Create file button clicked');
        }
        
        function testSaveFile() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر حفظ ملف';
            console.log('Save file button clicked');
        }
        
        function testRunCode() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر تشغيل كود';
            console.log('Run code button clicked');
        }
        
        function testFormatCode() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر تنسيق كود';
            console.log('Format code button clicked');
        }
        
        function testAnalyzeCode() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر تحليل كود';
            console.log('Analyze code button clicked');
        }
        
        function testSendChat() {
            document.getElementById('result').innerHTML = 'تم الضغط على زر إرسال رسالة';
            console.log('Send chat button clicked');
        }
    </script>
</body>
</html>
