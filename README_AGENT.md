# Ring Programming IDE - AI Powered Agent

## نظرة عامة

بيئة تطوير متكاملة للغة البرمجة Ring مدعومة بوكيل ذكي متقدم يستخدم نماذج الذكاء الاصطناعي الحديثة مثل Gemini وOpenAI وClaude لتقديم مساعدة برمجية احترافية.

## المميزات الرئيسية

### 🤖 الوكيل الذكي المتقدم
- **اتصال مباشر بـ APIs الذكاء الاصطناعي**: دعم Gemini، OpenAI، وClaude
- **هندسة السياق المتقدمة**: إدارة ذكية للمحادثات وسياق المشروع
- **أدوات شاملة**: قدرات متقدمة لإدارة الملفات والكود والمشاريع

### 🛠️ أدوات الوكيل المتقدمة
- **عمليات الملفات**: كتابة، قراءة، حذف، وإدارة الملفات
- **تشغيل الكود**: تنفيذ وتحليل وتنسيق كود Ring
- **إدارة المشاريع**: إنشاء وتحليل هياكل المشاريع
- **عمليات Git**: تهيئة وإدارة مستودعات Git
- **أوامر النظام**: تنفيذ أوامر النظام والبحث في الملفات

## التشغيل السريع

### 1. اختبار الوكيل الذكي
```bash
ring test_agent.ring
```

### 2. تشغيل التطبيق الكامل
```bash
ring main.ring
```

### 3. إعداد مفاتيح API
عدل ملف `config/api_keys.json` وأضف مفاتيح API:
```json
{
    "gemini": {
        "api_key": "YOUR_API_KEY_HERE",
        "enabled": true
    }
}
```

## هيكل المشروع

```
├── main.ring                 # نقطة دخول التطبيق
├── test_agent.ring          # اختبار سريع للوكيل
├── src/                     # الكود المصدري
│   ├── smart_agent.ring     # الوكيل الذكي الرئيسي
│   ├── ai_client.ring       # عميل الذكاء الاصطناعي
│   ├── context_engine.ring  # محرك السياق
│   ├── agent_tools.ring     # أدوات الوكيل
│   └── app.ring            # التطبيق الرئيسي
├── config/                  # ملفات التكوين
│   ├── api_keys.json       # مفاتيح API
│   └── prompt_templates.json # قوالب الـ prompts
└── assets/                 # الموارد والواجهة
```

## استخدام الوكيل الذكي

### الأوامر الأساسية
- **كتابة ملف**: "اكتب ملف جديد باسم test.ring"
- **تشغيل الكود**: "شغل هذا الكود"
- **تحليل الكود**: "حلل هذا الكود واعطني تقريراً"
- **إنشاء مشروع**: "أنشئ مشروع جديد باسم MyApp"

### الأدوات المتاحة (15+ أداة)
1. **عمليات الملفات**: write_file, read_file, delete_file, list_files
2. **عمليات الكود**: run_ring_code, analyze_code, format_code
3. **إدارة المشاريع**: create_project, analyze_project
4. **عمليات Git**: git_init, git_status, git_add, git_commit
5. **أوامر النظام**: execute_command, search_in_files

## الحالة الحالية

✅ **مكتمل**:
- نظام الوكيل الذكي الأساسي
- 15+ أداة متقدمة للملفات والكود
- عميل الذكاء الاصطناعي مع دعم APIs متعددة
- محرك السياق المتقدم
- نظام إدارة المحادثات

🔄 **قيد التطوير**:
- واجهة المستخدم المتكاملة
- تحسين أداء الأدوات
- إضافة المزيد من نماذج الذكاء الاصطناعي

## الاختبار

```bash
# اختبار سريع للوكيل
ring test_agent.ring

# اختبار الأدوات مباشرة
ring -c "load 'src/agent_tools.ring' oTools = new AgentTools() see oTools.getToolsList()"
```

---

**تم تطوير هذا المشروع بحب للمجتمع البرمجي العربي** ❤️
